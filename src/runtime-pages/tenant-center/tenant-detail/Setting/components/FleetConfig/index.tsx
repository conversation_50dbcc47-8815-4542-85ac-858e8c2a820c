import React, { useState, useEffect, useImperativeHandle, useRef, forwardRef } from 'react';
import { InfoPanel } from '@streamax/starry-components';
import { getAppGlobalData, i18n, useSystemComponentStyle, StarryAbroadFormItem as AFormItem } from '@base-app/runtime-lib';
import { Form, message, Tooltip, Radio, Switch } from '@streamax/poppy';
import { getTenantManagerConfig, postTenantManagerConfig } from '@/service/tenant';
import { IconInformation } from '@streamax/poppy-icons';

import './index.less';
import {useGetState} from "@streamax/hooks";

interface UserInfoConfigProps {
    belongToIsOpen: number;
    mustRelateIsOpen: number;
    autoAuthIsOpen: number;
}
const RADIO_OPEN = 1;
const RADIO_CLOSE = 0;
const Describe = (props: any) => (
    <Tooltip title={props.content} placement="right" className="question-icon">
        <IconInformation style={{ marginLeft: '8px' }} />
    </Tooltip>
);
function UserFleetConfig(props: any, ref: any) {
    const { openIsWhen } = props;
    const [editing, setEditing] = useState(false);
    const [checked, setChecked,getChecked] = useGetState(false);
    const [initParams, setInitParams] = useState<UserInfoConfigProps>({});
    const [form] = Form.useForm();
    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');

    useEffect(() => {
        getConfig();
    }, []);

    const judgeZero = (sourceValue: number | undefined | null) => {
        if (sourceValue === undefined || sourceValue === null) {
            return RADIO_CLOSE;
        } else {
            return sourceValue;
        }
    };
    const getConfig = async () => {
        const params = {
            tenantId: APP_USER_INFO.tenantId,
            keys: 'tenant.user.fleet.config',
        };

        try {
            const userConfigInfo = await getTenantManagerConfig(params); // 无值时返回 ：{}
            const info = userConfigInfo['tenant.user.fleet.config']
                ? JSON.parse(userConfigInfo['tenant.user.fleet.config'])
                : {};
            form.setFieldsValue({
                mustRelateIsOpen: judgeZero(info.mustRelateIsOpen),
                autoAuthIsOpen: judgeZero(info.autoAuthIsOpen),
            });
            setChecked(Boolean(judgeZero(info.belongToIsOpen)));
            setInitParams({
                belongToIsOpen: judgeZero(info.belongToIsOpen),
                mustRelateIsOpen: judgeZero(info.mustRelateIsOpen),
                autoAuthIsOpen: judgeZero(info.autoAuthIsOpen),
            });
        } catch (error) {
            message.error(i18n.t('message', '获取用户信息失败'));
        }
    };
    const backInitData = () => {
        form.setFieldsValue({
            mustRelateIsOpen: initParams.mustRelateIsOpen,
            autoAuthIsOpen: initParams.autoAuthIsOpen,
        });
        setChecked(Boolean(initParams.belongToIsOpen));
    };
    // 取消编辑
    function handleEditCancel() {
        backInitData();
        openIsWhen && openIsWhen(false);
        setEditing(false);
    }

    // 保存编辑
    function handleSave() {
        setEditing(false);
        form.submit();
    }

    useImperativeHandle(ref, () => ({
        resume: handleEditCancel,
    }));

    // 提交
    const onFinish = async (values: UserInfoConfigProps) => {
        const value = {
            belongToIsOpen: Number(getChecked()),
            // 关闭时 还是传上一次保存的值
            mustRelateIsOpen: !checked ? initParams.mustRelateIsOpen : values.mustRelateIsOpen,
            autoAuthIsOpen: !checked ? initParams.mustRelateIsOpen : values.autoAuthIsOpen,
        };
        const params = {
            tenantId: APP_USER_INFO.tenantId,
            configList: [
                {
                    key: 'tenant.user.fleet.config',
                    value,
                },
            ],
        };
        try {
            await postTenantManagerConfig(params);
            message.success(i18n.t('message', '设置成功'));
            setEditing(false);
            openIsWhen && openIsWhen(false);
            setInitParams(value);
        } finally {
            openIsWhen && openIsWhen(false);
            setEditing(false);
        }
    };

    const actionNodes = !editing ? (
        <a
            onClick={() => {
                openIsWhen && openIsWhen(true);
                setEditing(true);
            }}
        >
            {i18n.t('action', '编辑')}
        </a>
    ) : (
        <>
            <a onClick={handleEditCancel}>{i18n.t('action', '取消')}</a>
            <a onClick={handleSave}>{i18n.t('action', '保存')}</a>
        </>
    );

    const { isAbroadStyle } = useSystemComponentStyle();

    return (
        <InfoPanel
            title={<span>{i18n.t('name', '用户归属车组')}</span>}
            extraRight={actionNodes}
            extraTitle={
                <Switch
                    checked={checked}
                    disabled={!editing}
                    onChange={(checked: boolean) => setChecked(checked)}
                />
            }
            className="user-fleet-config"
        >
            <span className="tenant-user-fleet-config">
                *
                {i18n.t(
                    'message',
                    '开启后，添加用户需选择归属车组，授权车组后，能查看车组下的用户相关信息',
                )}
            </span>
            {
                checked && (
                    <Form form={form} onFinish={onFinish} layout="vertical" style={{ marginTop: 20 }}>
                        <AFormItem
                            name="mustRelateIsOpen"
                            className="radiobox-wraper-container"
                            label={
                                <div className="desc-wrapper">
                                    {i18n.t('name', '必须关联车组')}

                                    <Describe
                                        content={i18n.t(
                                            'message',
                                            '开启后，新增、编辑用户，必须选择归属车组',
                                        )}
                                    />
                                </div>
                            }
                        >
                            <Radio.Group disabled={!editing} className="radiobox-container">
                                <Radio value={RADIO_CLOSE}>{i18n.t('action', '关闭')}</Radio>
                                { isAbroadStyle & <br /> }
                                <Radio value={RADIO_OPEN}>{i18n.t('action', '开启')}</Radio>
                            </Radio.Group>
                        </AFormItem>
                        <AFormItem
                            name="autoAuthIsOpen"
                            className="radiobox-wraper-container"
                            label={
                                <div>
                                    {i18n.t('name', '自动授权')}
                                    <Describe
                                        content={
                                            <div>
                                                <div>
                                                    {i18n.t(
                                                        'message',
                                                        '开启后，新增用户时，会自动为用户授予归属车组数据权限。',
                                                    )}
                                                </div>
                                                <div>
                                                    {i18n.t(
                                                        'message',
                                                        '编辑用户时，会在授予新归属车组数据权限的同时，删除原归属车组数据权限',
                                                    )}
                                                </div>
                                            </div>
                                        }
                                    />
                                </div>
                            }
                        >
                            <Radio.Group disabled={!editing} className="radiobox-container">
                                <Radio value={RADIO_CLOSE}>{i18n.t('action', '关闭')}</Radio>
                                { isAbroadStyle & <br /> }
                                <Radio value={RADIO_OPEN}>{i18n.t('action', '开启')}</Radio>
                            </Radio.Group>
                        </AFormItem>
                    </Form>
                )
            }
        </InfoPanel>
    );
}
export default forwardRef(UserFleetConfig);
