import type { ReactElement } from 'react';
import { FC, useRef } from 'react';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import {Auth, i18n, mosaicManager, StarryAbroadFormItem, StarryAbroadIcon, StarryModal } from '@/runtime-lib';
import { Button, Form, message, Space, Switch, Tooltip, Spin } from '@streamax/poppy';
import { InfoPanel } from '@streamax/starry-components';
import { RspFormLayout } from '@streamax/responsive-layout';
import './index.less';
import { IconInformationFill, IconRequestFill } from '@streamax/poppy-icons';
import { useAsyncEffect, useLockFn, useRequest } from '@streamax/hooks';
import type { TenantPrivacyConfigData } from '@/service/tenant';
import type { RuleObject } from '@streamax/poppy/lib/form';
import { MAX_PRIVACY_USER_ROLE_COUNT } from '@/runtime-pages/tenant-center/const';
import InfoBack from '@/components/InfoBack';

type AuthSelectItem = { value: string }

export const enum FormField {
    mosaicSwitch = 'mosaicSwitch',
    customSwitch = 'customSwitch',
    specifyRoleSwitch = 'specifyRoleSwitch',
    specifyUserSwitch = 'specifyUserSwitch',
    roles = 'roles',
    users = 'users',
    defaultMosaicWhenCustom = 'defaultMosaicWhenCustom'
}

function handleSwitchValue(checked: boolean) {
    return checked ? 1 : 0;
}

function decodeStrToList(str: string): AuthSelectItem[] {
    if (!str) return [];
    return str.split(',').map(value => {
        return { value };
    });
}

function encodeListToStr(list: AuthSelectItem[]) {
    return list ? list.map(row => row.value ?? row).join(',') : '';
}

function validatorMaxRole(_: RuleObject, value: AuthSelectItem[]) {
    const LIMIT_NUM = MAX_PRIVACY_USER_ROLE_COUNT;
    if (value?.length > LIMIT_NUM) {
        return Promise.reject(
            new Error(
                i18n.t(
                    'message',
                    '最多选择{number}个角色',
                    { number: LIMIT_NUM },
                ),
            ),
        );
    }
    return Promise.resolve();
}

function validatorMaxUser(_: RuleObject, value: AuthSelectItem[]) {
    const LIMIT_NUM = 20;
    if (value?.length > LIMIT_NUM) {
        return Promise.reject(
            new Error(
                i18n.t(
                    'message',
                    '最多选择{number}个用户',
                    { number: LIMIT_NUM },
                ),
            ),
        );
    }
    return Promise.resolve();
}

type UserPrivacyConfigFormProps = {
    openIsWhen: (flag: boolean) => void,
    disabled?: boolean
    fetchInitData: () => Promise<TenantPrivacyConfigData>
    saveData: (values: TenantPrivacyConfigData) => Promise<boolean>
    roleSelect?: ReactElement
    userSelect?: ReactElement
    hasActionAuth?: boolean
    needValidateGpu?: boolean //是否对gpu资源进行校验
    actionAuthCode: string
}

export const UserPrivacyConfigForm = forwardRef((props: UserPrivacyConfigFormProps, ref) => {
    const { openIsWhen, disabled, roleSelect, actionAuthCode, userSelect, fetchInitData, saveData, needValidateGpu = false } = props;
    const [editing, setEditing] = useState(false);
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm<TenantPrivacyConfigData>();
    const initValuesRef = useRef<Partial<TenantPrivacyConfigData>>({});
    const { gpuTipVisible = false } = needValidateGpu && mosaicManager.useTenantMosaicShowGpuTip();
    function handleEditCancel() {
        form.setFieldsValue(initValuesRef.current);
        setEditing(false);
        openIsWhen?.(false);
    }

    function handleEdit() {
        openIsWhen && openIsWhen(true);
        setEditing(true);
    }

    async function _handleSave() {
        const values = await form.validateFields();
        const roles = values.roles ?? initValuesRef.current.roles ?? [];
        const users = values.users ?? initValuesRef.current.users ?? [];

        const mergeData = {
            ...initValuesRef.current, // 没在表单中的字段原样返回
            ...values,
            roles: encodeListToStr(roles as unknown as AuthSelectItem[]),
            users: encodeListToStr(users as unknown as AuthSelectItem[]),
        };
        await saveData(mergeData);
        initValuesRef.current = {
            ...initValuesRef.current,
            ...values,
        };
        message.success(i18n.t('message', '设置成功'));
        setEditing(false);
        openIsWhen?.(false);
    }

    const handleSave = useLockFn(_handleSave);

    const actionNodes = !editing ? (
        <Auth code={actionAuthCode}>
            <Button
                type="link"
                disabled={disabled}
                onClick={handleEdit}
                style={{
                    padding: 0,
                    height: 'unset',
                    fontWeight: 'normal',
                }}
            >
                {i18n.t('action', '编辑')}
            </Button>
        </Auth>
    ) : (
        <>
            <Auth code={actionAuthCode}>
                <a onClick={handleEditCancel}>
                    {i18n.t('action', '取消')}
                </a>
            </Auth>
            <Auth code={actionAuthCode}>
                <a onClick={handleSave}>
                    {i18n.t('action', '保存')}
                </a>
            </Auth>
        </>
    );

    useImperativeHandle(ref, () => ({
        resume: handleEditCancel,
        reload: handleEditCancel,
    }));
    useAsyncEffect(async () => {
        setLoading(true);
        try {
            const data = await fetchInitData();
            // @ts-ignore
            data.roles = decodeStrToList(data.roles as string);
            // @ts-ignore
            data.users = decodeStrToList(data.users as string);
            // 填充默认值
            const defaultValues = {
                mosaicSwitch: 0,
                customSwitch: 0,
                specifyRoleSwitch: 0,
                specifyUserSwitch: 0,
                defaultMosaicWhenCustom: 1
            };
            initValuesRef.current = {
                ...defaultValues,
                ...data,
            };

            form.setFieldsValue(initValuesRef.current);
            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    }, []);
    const onMosaicSwitchChange = (value) => {
        if (gpuTipVisible && value) {
            form.setFieldsValue({
                mosaicSwitch: 0,
            });
            const modal = StarryModal.confirm({
                centered: true,
                title: i18n.t('name', '操作确认'),
                content: i18n.t(
                    'message',
                    '系统检测到平台未部署或已停用 GPU资源，性能不足将导致在平台上查看视频图片时不会进行打码处理，仅支持导出、分享的视频图片展示马赛克，确认开启打码？',
                ),
                icon: <IconRequestFill />,
                onOk: async () => {
                    form.setFieldsValue({
                        mosaicSwitch: 1,
                    });
                    modal.destroy();
                },
            });
        }
    };
    return loading ? <Spin className='tenant-user-loading-spin' /> : <InfoPanel
        title={<span>{i18n.t('name', '用户隐私配置')}</span>}
        extraRight= {actionNodes}
        className="tenant-user-privacy-config"
    >
        <Form layout="horizontal" form={form} colon={false}>
            <Form.Item
                name={FormField.mosaicSwitch}
                label={i18n.t('name', '视频、图片打码')}
                getValueFromEvent={handleSwitchValue}
                valuePropName="checked"
                className="poppy-form-item-bold"
            >
                <Switch disabled={!editing} onChange={onMosaicSwitchChange} />
            </Form.Item>
            <div className='form-item-desc-total-tip'>
                {i18n.t(
                    'message',
                    '开启后，在平台上查看视频、图片会默认展示马赛克，可设置是否允许用户手动关闭',
                )}
            </div>
            <Form.Item
                dependencies={[FormField.mosaicSwitch]}
                noStyle
            >
                {
                    ({ getFieldValue }) => {
                        if (!getFieldValue(FormField.mosaicSwitch)) return null;
                        return (
                            <>
                                <div className="form-item-desc form-item-desc-loose">
                                    {gpuTipVisible && needValidateGpu ? (
                                        <InfoBack
                                            title={
                                                <div className='Info-back-content-wrap'>
                                                    <div>
                                                        {i18n.t(
                                                            'message',
                                                            '注：高级打码功能依赖 GPU 支持，由于平台未部署或已停用GPU资源，将导致以下问题',
                                                        )}
                                                        ：
                                                    </div>
                                                    <div>
                                                        {i18n.t(
                                                            'message',
                                                            '1、平台内查看视频/图片时无法展示马赛克（需依赖设备自身打码功能保障隐私安全）',
                                                        )}
                                                    </div>
                                                    <div>
                                                        {i18n.t(
                                                            'message',
                                                            '2、仅支持导出、分享的视频/图片展示马赛克',
                                                        )}
                                                    </div>
                                                </div>
                                            }
                                        />
                                    ) : null}
                                </div>

                                <Form.Item
                                    name={FormField.customSwitch}
                                    label={i18n.t('name', '用户自定义打码')}
                                    valuePropName="checked"
                                    getValueFromEvent={handleSwitchValue}
                                    className="poppy-form-item-bold"
                                >
                                    <Switch disabled={!editing} />
                                </Form.Item>
                                <div className="form-item-desc">
                                    {i18n.t(
                                        'message',
                                        '开启后允许所有用户在个人中心-马赛克设置中设置是否展示打码内容，支持单独指定可手动配置的角色和用户',
                                    )}
                                </div>
                                <Form.Item
                                    dependencies={[FormField.customSwitch]}
                                    noStyle
                                >
                                    {({ getFieldValue }) => {
                                        if (
                                            !getFieldValue(
                                                FormField.customSwitch,
                                            )
                                        )
                                            return null;
                                        return (
                                            <>
                                                <div className="form-item-user">
                                                    <Form.Item
                                                        label={i18n.t(
                                                            'name',
                                                            '指定角色',
                                                        )}
                                                    >
                                                        <Space
                                                            size={16}
                                                            align={'center'}
                                                        >
                                                            <Form.Item
                                                                name={
                                                                    FormField.specifyRoleSwitch
                                                                }
                                                                valuePropName="checked"
                                                                noStyle
                                                                getValueFromEvent={
                                                                    handleSwitchValue
                                                                }
                                                            >
                                                                <Switch
                                                                    disabled={
                                                                        !editing
                                                                    }
                                                                />
                                                            </Form.Item>

                                                            <Tooltip
                                                                title={i18n.t(
                                                                    'message',
                                                                    '指定角色关联的用户支持自定义是否展示打码内容',
                                                                )}
                                                                placement="right"
                                                            >
                                                                <span>
                                                                    <StarryAbroadIcon>
                                                                        <IconInformationFill className="tips-icon-information" />
                                                                    </StarryAbroadIcon>
                                                                </span>
                                                            </Tooltip>
                                                        </Space>
                                                    </Form.Item>
                                                    <Form.Item
                                                        dependencies={[
                                                            FormField.specifyRoleSwitch,
                                                        ]}
                                                        noStyle
                                                    >
                                                        {({
                                                            getFieldValue,
                                                        }) => {
                                                            if (
                                                                !getFieldValue(
                                                                    FormField.specifyRoleSwitch,
                                                                )
                                                            )
                                                                return null;
                                                            return (
                                                                <RspFormLayout layoutType="fixed">
                                                                    <RspFormLayout.Col>
                                                                        <StarryAbroadFormItem
                                                                            name={
                                                                                FormField.roles
                                                                            }
                                                                            className="custom-form-item"
                                                                            rules={[
                                                                                {
                                                                                    required:
                                                                                        true,
                                                                                    message:
                                                                                        i18n.t(
                                                                                            'name',
                                                                                            '指定角色不能为空',
                                                                                        ),
                                                                                },
                                                                                {
                                                                                    validator:
                                                                                        validatorMaxRole,
                                                                                },
                                                                            ]}
                                                                            style={{
                                                                                marginBottom: 0,
                                                                            }}
                                                                        >
                                                                            {roleSelect &&
                                                                                React.cloneElement(
                                                                                    roleSelect,
                                                                                    {
                                                                                        disabled:
                                                                                            !editing,
                                                                                    },
                                                                                )}
                                                                        </StarryAbroadFormItem>
                                                                    </RspFormLayout.Col>
                                                                </RspFormLayout>
                                                            );
                                                        }}
                                                    </Form.Item>
                                                </div>

                                                {!userSelect ? null : (
                                                    <Form.Item
                                                        label={i18n.t(
                                                            'name',
                                                            '指定用户',
                                                        )}
                                                    >
                                                        <Space
                                                            size={16}
                                                            align={'center'}
                                                        >
                                                            <Form.Item
                                                                name={
                                                                    FormField.specifyUserSwitch
                                                                }
                                                                valuePropName="checked"
                                                                noStyle
                                                                getValueFromEvent={
                                                                    handleSwitchValue
                                                                }
                                                            >
                                                                <Switch
                                                                    disabled={
                                                                        !editing
                                                                    }
                                                                />
                                                            </Form.Item>

                                                            <Tooltip
                                                                title={i18n.t(
                                                                    'message',
                                                                    '指定用户支持自定义是否展示打码内容',
                                                                )}
                                                                placement="right"
                                                            >
                                                                <span>
                                                                    <StarryAbroadIcon>
                                                                        <IconInformationFill className="tips-icon-information" />
                                                                    </StarryAbroadIcon>
                                                                </span>
                                                            </Tooltip>
                                                        </Space>
                                                    </Form.Item>
                                                )}

                                                <Form.Item
                                                    dependencies={[
                                                        FormField.specifyUserSwitch,
                                                    ]}
                                                    noStyle
                                                >
                                                    {({ getFieldValue }) => {
                                                        if (
                                                            !getFieldValue(
                                                                FormField.specifyUserSwitch,
                                                            )
                                                        )
                                                            return null;
                                                        return (
                                                            <RspFormLayout layoutType="fixed">
                                                                <RspFormLayout.Col>
                                                                    <StarryAbroadFormItem
                                                                        name={
                                                                            FormField.users
                                                                        }
                                                                        className="custom-user-form-item"
                                                                        rules={[
                                                                            {
                                                                                required:
                                                                                    true,
                                                                                message:
                                                                                    i18n.t(
                                                                                        'message',
                                                                                        '指定用户不能为空',
                                                                                    ),
                                                                            },
                                                                            {
                                                                                validator:
                                                                                    validatorMaxUser,
                                                                            },
                                                                        ]}
                                                                    >
                                                                        {userSelect &&
                                                                            React.cloneElement(
                                                                                userSelect,
                                                                                {
                                                                                    disabled:
                                                                                        !editing,
                                                                                },
                                                                            )}
                                                                    </StarryAbroadFormItem>
                                                                </RspFormLayout.Col>
                                                            </RspFormLayout>
                                                        );
                                                    }}
                                                </Form.Item>
                                                <Form.Item
                                                    name={FormField.defaultMosaicWhenCustom}
                                                    label={i18n.t('name', '视频、图片默认打码')}
                                                    valuePropName="checked"
                                                    getValueFromEvent={handleSwitchValue}
                                                    className="poppy-form-item-bold poppy-form-item-default-mosaic"
                                                >
                                                    <Switch disabled={!editing} />
                                                </Form.Item>
                                                <div className="form-item-desc">
                                                    {i18n.t(
                                                        'message',
                                                        '关闭后，被允许自定义打码的用户默认查看无码视频和图片，支持用户手动开启打码',
                                                    )}
                                                </div>
                                            </>
                                        );
                                    }}
                                </Form.Item>

                            </>
                        );
                    }
                }
            </Form.Item>
        </Form>
    </InfoPanel>;
});
