@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';

.tenant-user-loading-spin {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.tenant-user-privacy-config {
    .starry-info-panel-header {
        padding-bottom: 20px;
    }
    .form-item-desc-total-tip {
        color: @starry-text-color-secondary;
        font-weight: 400;
        font-size: 12px;
    }
    .form-item-desc {
        margin-bottom: 24px;
        color: @starry-text-color-secondary;
        font-weight: 400;
        font-size: 12px;
        &:last-child {
            margin-bottom: 0;
        }
    }
     .form-item-desc-loose {
        font-size: 14px;
        margin-top: 12px;
        .Info-back-content-wrap {
            >div {
                margin-bottom: 8px;
            }
            >div:last-child{
                margin-bottom: 0;
            }
        }
    }
    .tips-icon-information > svg {
        color: @text-color-secondary;
    }

    .tips-icon-information > svg:hover {
        color: @primary-color;
    }

    .poppy-form-item {
        margin-bottom: 11px;
    }

    .poppy-form-item-bold {
        font-weight: 500;
    }
    .poppy-form-item-default-mosaic {
        margin-top: 24px;
    }

    .form-item-user {
        margin-bottom: 24px;
    }
    .custom-user-form-item {
        margin-bottom: 0px;
    }
}
