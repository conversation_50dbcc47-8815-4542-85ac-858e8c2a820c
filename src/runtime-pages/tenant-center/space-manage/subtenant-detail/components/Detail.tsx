/* eslint-disable consistent-return */
import { useState, useRef } from 'react';
import moment from 'moment';
import { Button, ProTable, Modal, Form, message, Select, Input } from '@streamax/poppy';
import type { TableProps } from '@streamax/poppy/lib/table';
import { i18n, utils, StarryAbroadFormItem } from '@base-app/runtime-lib';
import { ListDataContainer } from '@streamax/starry-components';
import { getTenantSetMealPage, Sets } from '@/service/tenant';
import { openMealServer } from '@/service/meal';
import DateRange from '@/components/DateRange';
import useFilterStorageSetMeal from '@/runtime-pages/tenant-center/hooks/useFilterStorageSetMeal';
import { getUserPage } from '@/service/user';
import { groupBy } from 'lodash';
import { SIZE_UNIT } from '@/utils/constant';
import { getPickerRanges } from '@/utils/commonFun';

interface Props {
    tenantId: number;
    statisticsSwitch: boolean;
    onGetTenantDetailSuccess?: (capacity: string) => void; // 当请求租户完成，吧当前租户分配的空间总数抛出，单位 GB
}

const {
    formator: { zeroTimeStampToFormatTime },
} = utils;
const SIZE_GB = 1;
const STORAGE_SET_MEAL = '3'; // 存储套餐
const { timestampToZeroTimeStamp } = utils.formator;
export default (props: Props) => {
    const { tenantId, onGetTenantDetailSuccess, statisticsSwitch } = props;
    const listDataContainerRef = useRef<Record<'loadDataSource', (params?: any) => void>>(null);
    const [modalOpen, setModalOpen] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [form] = Form.useForm();
    const { option } = useFilterStorageSetMeal(modalOpen);

    const tableColumns: any = [
        {
            title: i18n.t('name', '开通套餐'),
            dataIndex: 'setsName',
            ellipsis: true,
            render: (text: string) => text || '-',
        },
        {
            title: i18n.t('name', '分配容量(GB/TB)'),
            dataIndex: 'extend',
            ellipsis: true,
            render: (text: string) => {
                if (!statisticsSwitch) {
                    return '-';
                }
                try {
                    const { capacity, unit } = JSON.parse(text);
                    return `${capacity}${SIZE_UNIT[unit]}`;
                } catch (error) {
                    return '-';
                }
            },
        },
        {
            title: i18n.t('name', '分配时间'),
            dataIndex: 'createTime',
            ellipsis: true,
            sorter: true,
            sortDirections: ['ascend', 'descend', null],
            render: (text: number) => zeroTimeStampToFormatTime(text),
        },
        {
            title: i18n.t('name', '操作人'),
            // 接口定义里无字段 联调时需要注意
            dataIndex: 'operateUser',
            ellipsis: true,
            render: (text: string) => text || '-',
        },
    ];

    const items = [
        {
            label: i18n.t('name', '套餐名称'),
            name: 'setsName',
            field: Input,
            fieldProps: {
                placeholder: i18n.t('name', '请输入套餐名称'),
                width: 280,
                maxLength: 50,
            },
        },
        {
            label: i18n.t('name', '分配时间'),
            name: 'time',
            field: DateRange,
            colSize: 2,
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~ ',
                    allowClear: true,
                    ranges: getPickerRanges(),
                    style: {
                        width: '100%',
                    }
                },
            },
        },
    ];
    const handleTableChange: TableProps['onChange'] = (pagination, filters, sorter) => {
        listDataContainerRef.current?.loadDataSource({
            complexSort: `orderBy ${(sorter as { field: string }).field} ${
                (sorter as { order: string }).order === 'ascend' ? 'asc' : 'desc'
            }`,
        });
    };
    const calcTotalSize = (tableData: Sets[]) => {
        try {
            let totalSize = 0;
            tableData.forEach((item) => {
                const { capacity, unit } = JSON.parse(item.extend);
                if (SIZE_GB == unit) {
                    totalSize = totalSize + Number(capacity);
                } else {
                    totalSize = totalSize + Number(capacity) * 1024;
                }
            });
            onGetTenantDetailSuccess && onGetTenantDetailSuccess(totalSize + '' + 'GB');
        } catch (error) {
            onGetTenantDetailSuccess && onGetTenantDetailSuccess('');
        }
    };
    const fetchData = async (params: any) => {
        const { setsName, time, page, pageSize, complexSort } = params;
        let startTime, endTime;
        if (time) {
            startTime = timestampToZeroTimeStamp(time[0]);
            endTime = timestampToZeroTimeStamp(time[1]);
        }
        const {
            list: tenantRes = [],
            page: TPage,
            pageSize: TPageSize,
            total,
        } = await getTenantSetMealPage({
            setsName,
            setsTypes: STORAGE_SET_MEAL,
            complexSort: complexSort ?? 'orderBy createTime desc',
            startTime,
            endTime,
            tenantId,
            page,
            pageSize,
        });
        const userArr = Array.from(new Set(tenantRes.map((item) => item.createUser))) || [];
        const userRes = await getUserPage({
            page: 1,
            pageSize: 1e8,
            userIds: userArr.join(','),
        });
        const userIdMap = groupBy(userRes.list || [], 'userId');
        const tableData = tenantRes.map((item) => ({
            ...item,
            operateUser: userIdMap[item.createUser]?.[0]?.account || '-',
        }));
        onGetTenantDetailSuccess && calcTotalSize(tableData);
        return {
            list: tableData,
            page: TPage,
            pageSize: TPageSize,
            total,
        };
    };

    const handleAllocate = () => setModalOpen(true);

    const handleOk = () => {
        form.submit();
    };

    const handleCancel = () => {
        setModalOpen(false);
        form.resetFields();
    };

    const handleFinish = (values: Record<'setsId', number>) => {
        setConfirmLoading(true);
        openMealServer({
            tenantId,
            setsId: values.setsId,
        })
            .then((rs) => {
                if (!rs) return;
                message.success(i18n.t('name', '操作成功'));
                setModalOpen(false);
                listDataContainerRef.current?.loadDataSource();
                form.resetFields();
            })
            .finally(() => setConfirmLoading(false));
    };

    return (
        <div>
            <ListDataContainer
                ref={listDataContainerRef}
                getDataSource={fetchData}
                // pagination={{
                //     size: 'small',
                // }}
                queryForm={{
                    items,
                }}
                toolbar={{
                    extraLeft: statisticsSwitch ? (
                        <Button type="primary" onClick={handleAllocate}>
                            {i18n.t('name', '空间分配')}
                        </Button>
                    ) : null,
                }}
                listRender={(data) => {
                    return (
                        <ProTable
                            aroundBordered
                            columns={tableColumns}
                            dataSource={data}
                            pagination={false}
                            rowKey={'setsId' + 'createTime'}
                            onChange={handleTableChange}
                        />
                    );
                }}
            />
            <Modal
                width={420}
                title={i18n.t('name', '空间分配')}
                visible={modalOpen}
                onOk={handleOk}
                onCancel={handleCancel}
                confirmLoading={confirmLoading}
                getContainer={false}
                destroyOnClose
            >
                <Form layout="vertical" form={form} onFinish={handleFinish}>
                    <StarryAbroadFormItem
                        name="setsId"
                        label={i18n.t('name', '套餐')}
                        rules={[{ required: true }]}
                        style={{marginBottom:0}}
                    >
                        <Select
                            placeholder={i18n.t('message', '请选择套餐')}
                            options={option}
                        />
                    </StarryAbroadFormItem>
                </Form>
            </Modal>
        </div>
    );
};
