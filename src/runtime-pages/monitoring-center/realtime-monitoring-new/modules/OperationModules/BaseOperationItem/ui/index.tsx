import { BaseOperationItemProps } from '../interface';
import './index.less';
import { Badge, message, Popover, Spin } from '@streamax/poppy';
import { i18n, StarryAbroadIcon } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { getDeviceStates } from '@/utils/commonFun';
import { useDebounceFn } from 'ahooks';
import { RightOutlined } from '@ant-design/icons';

const BaseOperationItem = (props: BaseOperationItemProps) => {
    const {
        operationKey: key,
        name,
        icon,
        showDeviceList,
        deviceList,
        onClick,
        loading,
        vehicleStateConfig,
        showState,
        validateOffLine = true,
    } = props;

    const { run: onClickOperationBounce } = useDebounceFn(onClick, {
        wait: 600,
        leading: true,
        trailing: false,
    });
    /** 设备popover */
    const getDevicePopover = (deviceList: any[], key: any) => {
        return (
            <Spin spinning={loading}>
                <div className="operation-group">
                    {deviceList.map((device: any) => {
                        if (!device) return null;
                        const deviceState = getDeviceStates(
                            device,
                            vehicleStateConfig,
                        );
                        const badgeColor =
                            deviceState?.[0]?.stateColor || '#D9D9D9';
                        return (
                            <div
                                key={device.deviceId}
                                className="operation-item"
                                onClick={async (e: any) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    onClickOperationBounce(key, {
                                        ...device,
                                        showState,
                                        validateOffLine,
                                    });
                                }}
                            >
                                <OverflowEllipsisContainer tooltip={false}>
                                    <Badge
                                        color={badgeColor}
                                        className="device-status-badge"
                                        style={{
                                            width: 24,
                                            textAlign: 'center',
                                        }}
                                    />
                                    {loading
                                        ? ''
                                        : device.deviceAlias || device.deviceNo}
                                </OverflowEllipsisContainer>
                            </div>
                        );
                    })}
                </div>
            </Spin>
        );
    };
    const renderItem = (
        <div
            className="operation-item"
            onClick={async (e: any) => {
                e.stopPropagation();
                e.preventDefault();
                if (showDeviceList && deviceList.length > 1) {
                    return;
                } else {
                    const device = deviceList[0];
                    onClickOperationBounce(key, {
                        ...device,
                        showState,
                        validateOffLine,
                        deviceList
                    });
                }
            }}
            key={key}
        >
            <span className="operation-item-icon">
                <StarryAbroadIcon>{icon}</StarryAbroadIcon>
            </span>
            <OverflowEllipsisContainer tooltip={false}>
                {name}
            </OverflowEllipsisContainer>
            {showDeviceList && deviceList.length > 1 && (
                <span className="operation-item-other-info">
                    {/* <IconMore /> */}
                    <RightOutlined className="arrow-right-icon" />
                </span>
            )}
        </div>
    );

    if (showDeviceList && deviceList.length > 1) {
        return (
            <Popover
                placement="rightTop"
                overlayClassName="base-operation-popover"
                key={key}
                content={getDevicePopover(deviceList, key)}
                overlayStyle={{ padding: 0 }}
            >
                {renderItem}
            </Popover>
        );
    } else {
        return renderItem;
    }
};

export default BaseOperationItem;
