import {
    Input,
    Select,
    Button,
    message,
    Space,
    Badge,
    Switch,
    Tooltip,
    Form,
} from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import type { ProTableToolBarProps } from '@streamax/poppy/lib/pro-table';
import type { ColumnsType } from '@streamax/poppy/lib/table';
import {
    IconRequestFill,
    IconDeleteFill,
    IconStopFill,
    IconAddFill,
    IcListEditFill,
    IconExport,
    IconInformationFill,
    IconUnbind03Line,
} from '@streamax/poppy-icons';
import {
    i18n,
    Auth,
    utils,
    useUrlSearchStore,
    getAppGlobalData,
    StarryAbroadOverflowEllipsisContainer,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import {
    StarryTable,
    StarryCard,
    StarryBreadcrumb,
    StarryModal,
    Action,
    // @ts-ignore
} from '@base-app/runtime-lib';
import { Link, useHistory } from '@base-app/runtime-lib/core';
import moment from 'moment';
import { useState, useRef, useEffect } from 'react';
import { getUserPage, deleteUser, lockUser, unlockUser, queryApproverByUserId, otpResetUnbind, OTPStateEnum, otpStatusQuery } from '../../service/user';
import { exportExcel } from '@/service/import-export';
import { fetchTenantPwdConfig, getUserInfoConfig } from '@/service/tenant';
import './index.less';
import {USER_DORMANCY, USER_ENABLE, USER_EXPIRE, USER_UNABLE} from '@/utils/constant';
import { isNil } from 'lodash';
import { RefStarryTableProps } from '@/runtime-lib/components/StarryTable';
import { useGetState } from 'ahooks';
import ActivateUserModal from "@/runtime-pages/user-manage/components/ActivateUserModal";
import DateRange from "@/components/DateRange";
import {getPickerRangeTWM} from "@/utils/date-picker-config";
import {getUserExpireDate} from "@/runtime-lib/utils/userExpirationUtil";
import {useTimeUrlStorageTransform} from "@/hooks/useTimeUrlStorageTransform";
import { useUserInfoConfig } from './hooks/useUserInfoConfig';
import {getDomainName} from "@/runtime-lib/utils/commonFun";

const { confirm } = StarryModal;
const { zeroTimeStampToFormatTime, timestampToZeroTimeStamp } = utils.formator;

export default () => {
    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');
    const searchStore = useUrlSearchStore();
    const { page: pageStr=1, pageSize: pageSizeStr=20, account, state , expireTimeStart, expireTimeEnd} = searchStore.get();
    const page = Number(pageStr);
    const pageSize = Number(pageSizeStr);
    const [form] = Form.useForm();
    const [visible,setVisible] = useState<boolean>(false);
    const { highRiskOperationSwitch } = useUserInfoConfig();

    const { localTimeTransform, serverTimeTransform } = useTimeUrlStorageTransform();
    const [isOpenLockConfig, setIsOpenLockConfig] = useState<boolean>();
    const [userState, setUserState, getUserState] = useGetState([
        {
            text: i18n.t('state', '启用'),
            value: USER_ENABLE,
            status: 'success',
        },
        {
            text: i18n.t('state', '停用'),
            value: USER_UNABLE,
            status: 'default',
        },
    ]);

    const tableRef = useRef<RefStarryTableProps | null>(null);
    const [prevLoaded, setPrevLoaded] = useState<boolean>(false);
    const [belongToIsOpen, setBelongToIsOpen, getBelongToIsOpen] = useGetState<boolean>(false);
    const [otpSwitch, setOtpSwitch, getOtpSwitch] = useGetState<boolean>(false);
    const [userAutoExpireEnable, setUserAutoExpireEnable, getUserAutoExpireEnable] = useGetState<boolean>();
    const otpStateObject = {
        [OTPStateEnum.BOUND]: {
            title: i18n.t('state', '已绑定'),
            state: 'success'
        },
        [OTPStateEnum.UNBOUND]: {
            title: i18n.t('state', '未绑定'),
            state: 'warning'
        },
        [OTPStateEnum.UNSUPPORTED]: {
            title: i18n.t('state', '无需绑定'),
            state: 'error'
        }
    };
    useEffect(() => {
        let newState = Number(state) || undefined;
        if (!getUserState()?.find(item=>item.value==USER_DORMANCY) && newState ==USER_DORMANCY) {
            newState = undefined;
        }
        if (!getUserState()?.find(item=>item.value==USER_EXPIRE) && newState ==USER_EXPIRE) {
            newState = undefined;
        }
        prevLoaded && tableRef.current?.loadDataSource?.({
            page: Number(page),
            pageSize: Number(pageSize),
            account,
            state: newState,
            ...(getUserAutoExpireEnable()?{ expireTime: localTimeTransform(expireTimeStart, expireTimeEnd),}:{})
        });
    }, [prevLoaded]);

    useEffect(() => {
        getTenantPasswordConfig();
    }, []);
    useEffect(() => {
        if (typeof isOpenLockConfig === 'boolean' && typeof userAutoExpireEnable === 'boolean') {
            if (!getUserState()?.find(item=>item.value==USER_DORMANCY) && state ==USER_DORMANCY) {
                return;
            }
            if (!getUserState()?.find(item=>item.value==USER_EXPIRE) && state ==USER_EXPIRE) {
                return;
            }
            // 有值时状态下拉选项已经确定，再回显不会在一瞬间展示数字
            form.setFieldsValue({ account, state: Number(state) || null,
                ...(getUserAutoExpireEnable()?{ expireTime: localTimeTransform(expireTimeStart, expireTimeEnd),}:{})
            });
        }
    }, [isOpenLockConfig,userAutoExpireEnable]);
    // 选中的用户
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRecord, setSelectedRecord] = useState<any>();
    const [lastQueryParams, setLastQueryParams] = useState({});
    const [list, setList] = useState([]);
    const [longTermNoLoginLock, setLongTermNoLoginLock] = useState();

    // 查询租户密码配置
    const getTenantPasswordConfig = async () => {
        const params = {
            tenantId: APP_USER_INFO.tenantId,
            keys: 'tenant.user.fleet.config,tenant.user.state.config',
        };
        try {
            const userConfigInfo = await getUserInfoConfig(params); // 无值时返回 ：{}

            const { userAutoExpireSwitch, longTermNoLoginLock } = JSON.parse(userConfigInfo?.['tenant.user.state.config'] || "{}");
            const newState = [...userState];
            if (typeof longTermNoLoginLock === 'number') {
                // 开启过，下拉选项有休眠
                newState.push({
                    text: i18n.t('state', '休眠'),
                    value: USER_DORMANCY,
                    status: 'default',
                });
            }
            if (longTermNoLoginLock) {
                setIsOpenLockConfig(true);
            } else {
                setIsOpenLockConfig(false);
            }
            setLongTermNoLoginLock(longTermNoLoginLock);
            // 车组配置参数
            const { belongToIsOpen } = JSON.parse(userConfigInfo?.['tenant.user.fleet.config'] || "{}");
            setBelongToIsOpen(!!belongToIsOpen);

            if (userAutoExpireSwitch){
                newState.push({
                    text: i18n.t('state', '到期'),
                    value: USER_EXPIRE,
                    status: 'default',
                });
            }
            setUserState(newState);
            setUserAutoExpireEnable(Boolean(userAutoExpireSwitch));
            /***获取opt验证开关状态***/ 
            await queryOtpSwitch();
        } catch (error) { }
        finally {
            setPrevLoaded(true);
        }
    }; 
const queryOtpSwitch = async () => {
    const { tenantOtpStatus } = await otpStatusQuery({
        domainName: getDomainName(),
    });
    setOtpSwitch(tenantOtpStatus);
};
    
const fetchUserList = async (params: any) => {
    setSelectedRowKeys([]);
    const { expireTime } = params;
    // 使用 serverTimeTransform 处理日期范围，但确保开始时间为当天的0点，结束时间为当天的23:59:59
    let expireTimeStart, expireTimeEnd;
    if (expireTime && expireTime.length) {
        // 设置开始日期为当天的0点
        const startDate = expireTime[0].clone().startOf('day');
        // 设置结束日期为当天的23:59:59
        const endDate = expireTime[1].clone().endOf('day');
        [expireTimeStart, expireTimeEnd] = [timestampToZeroTimeStamp(startDate), timestampToZeroTimeStamp(endDate)];
    } else {
        [expireTimeStart, expireTimeEnd] = [undefined, undefined];
    }
    delete params.expireTime;
    const timeParam = getUserAutoExpireEnable()?{ expireTimeStart,expireTimeEnd,needExpireInfo: true}: {expireTimeStart: undefined,expireTimeEnd:undefined};
        let requestFiles = '';
        if(getBelongToIsOpen()) {
            requestFiles += 'belongFleet,';
        }
        if(getOtpSwitch()) {
            requestFiles += 'otpState';
        }
        const queryParams = {
            ...params,
            account: (params?.account || '').trim(),
            fields: requestFiles,
            ...timeParam,
            needApprover: true,
        };
        const { list, total } = await getUserPage(queryParams);
        setLastQueryParams(queryParams);
        searchStore.set({
            ...queryParams,
        });
        
        const res = {
            list,
            total,
        };
        setList(list);
        return Promise.resolve(res);
    };

    // 删除，单个，批量操作
    const handleDelte = (record: any) => {
        const { userId, account } = record;
        // 是否批量操作
        const isMul = !userId;
        let title = i18n.t('name', '删除确认');
        let content = i18n.t('message', '确认要删除“{name}”用户吗？', { name: account });
        if (isMul) {
            title = i18n.t('name', '删除确认');
            content = i18n.t('message', '确认要删除已选用户名称的用户吗？');
        }
        if (isMul && selectedRowKeys.length === 0) {
            message.info(i18n.t('message', '请选择用户'));
            return;
        }
        const params = {
            userList: [userId],
        };
        if (isMul) {
            params.userList = selectedRowKeys;
        }
        confirm({
            size:'small',
            title,
            content,
            icon: <IconRequestFill />,
            centered: true,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () =>
                new Promise<void>((resolve, reject) => {
                    deleteUser(params)
                        .then((data: any) => {
                            if (data) {
                                message.success(i18n.t('message', '操作成功'));
                                resolve();
                                // @ts-ignore
                                tableRef.current.reload();
                            } else {
                                message.error(i18n.t('message', '操作失败'));
                                reject();
                            }
                        })
                        .catch(() => {
                            reject();
                        });
                }),
        });
    };

    const activateUser = (record: any) => {
        setVisible(true);
        setSelectedRecord(record);
    };

    // 启用/停用用户(单个、批量)
    const lockOrUnlock = (lock: boolean, record: any = {}) => {
        const { userId, account } = record;

        // 是否批量操作
        const isMul = !userId;
        if (isMul && selectedRowKeys.length === 0) {
            message.info(i18n.t('message', '请选择用户'));
            return;
        }

        const params = {
            userId,
        };
        let title = i18n.t('name', '操作确认');
        let content = lock
            ? i18n.t('message', '确认要停用“{name}”吗？', { name: account })
            : i18n.t('message', '确认要启用“{name}”吗？', { name: account });
        if (isMul) {
            title = lock ? i18n.t('name', '停用') : i18n.t('name', '启用');
            content = lock
                ? i18n.t('message', '确定要停用已选用户吗？')
                : i18n.t('message', '确定要启用已选用户吗？');
            const list1 = list.filter(({ userId, type }) => {
                return selectedRowKeys.includes(userId) && type != 2;
            });
            const list2 = list1.map(({ userId }) => userId);
            params.userId = list2.join(',');
        }
        if (record.state === USER_DORMANCY) {
            content = i18n.t(
                'message',
                '确认要启用“{name}”吗？启用后，用户状态变为启用，可以正常登录平台。',
                { name: account },
            );
        }
        confirm({
            size:'small',
            title,
            content,
            icon: <IconRequestFill />,
            centered: true,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () =>
                new Promise<void>((resolve, reject) => {
                    (lock ? lockUser(params) : unlockUser(params))
                        .then((data: any) => {
                            if (data) {
                                message.success(i18n.t('message', '操作成功'));
                                resolve();
                                // @ts-ignore
                                tableRef.current.reload();
                            } else {
                                message.error(i18n.t('message', '操作失败'));
                                reject();
                            }
                        })
                        .catch(() => {
                            reject();
                        });
                }),
        });
    };
    /**解绑otp**/
    const handleUnbound = (record) => {
        confirm({
            title: i18n.t('name', '解绑确认'),
            content: i18n.t(
                'message',
                '确认要解绑用户“{account}”的OTP吗？解绑后用户重新登录时可绑定新的OTP',
                {
                    account: record.account,
                },
            ),
            icon: <IconRequestFill />,
            centered: true,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () =>
                new Promise<void>((resolve, reject) => {
                    otpResetUnbind({
                        userId: record.userId,
                    })
                        .then((data: any) => {
                            if (data) {
                                message.success(i18n.t('message', '操作成功'));
                                resolve();
                                // @ts-ignore
                                tableRef.current.reload();
                            } else {
                                message.error(i18n.t('message', '操作失败'));
                                reject();
                            }
                        })
                        .catch(() => {
                            reject();
                        });
                }),
        });
    };
    const formItems: QueryFormProps['items'] = [
        {
            label: i18n.t('name', '用户'),
            name: 'account',
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入用户名'),
                maxLength: 50,
            },
            itemProps: {
                rules: [{ max: 50, type: 'string' }],
            },
        },
        {
            label: i18n.t('name', '状态'),
            name: 'state',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择用户状态'),
                options: userState.map(({ text, value }) => ({
                    value,
                    label: text,
                })),
            },
        },
        userAutoExpireEnable && {
            label: i18n.t('name', '到期日期'),
            name: 'expireTime',
            colSize: 2,
            field: DateRange,
        fieldProps: {
            maxInterval: {
                value: 'Infinity',
                unitOfTime: 'days',
            },
            allowSameTime: true, // 允许选择同一天
            pickerProps: {
                allowClear: true,
                separator: '~  ',
                ranges: getPickerRangeTWM(),
                style: {
                    width: '100%',
                }
            },
        },
        },
    ].filter(Boolean)
    ;
    const columns: ColumnsType<any> = [
        {
            title: i18n.t('name', '用户名称'),
            dataIndex: 'account',
            ellipsis: { showTitle: false },
            fixed: 'left',
            width: 220,
            render: (text, record) => (
                <StarryAbroadOverflowEllipsisContainer>
                    <Action
                        code="@base:@page:user.manage@action:detail"
                        url="/user-manage/user-detail"
                        fellback={text}
                        params={{
                            userId: record.userId,
                        }}
                    >
                        {text}
                    </Action>
                </StarryAbroadOverflowEllipsisContainer>
            ),
        },
        {
            title: (
                <span className="user-manage-table-header-tip-icon-box">
                    <span>{i18n.t('name', '状态')}</span>
                    {typeof longTermNoLoginLock === 'undefined' ? null : (
                        <Tooltip
                            placement="right"
                            title={i18n.t('name', '长时间未登录的用户处于休眠状态，且账号自动停用')}
                        >
                            <span>
                                <StarryAbroadIcon>
                                    <IconInformationFill className="user-manage-table-header-tip-icon" />
                                </StarryAbroadIcon>
                            </span>
                        </Tooltip>
                    )}
                </span>
            ),
            dataIndex: 'state',
            // filters: userState,
            width: 180,
            render: (text: number, record) => {
                let status: 'success' | 'default' | 'error' = 'default';
                let showText = '-';
                userState.forEach((item) => {
                    if (item.value === text) {
                        // @ts-ignore
                        status = item.status;
                        showText = item.text;
                    }
                });
                return (
                    <Space>
                        <Badge status={status} text={showText} />
                {/* 如果状态是USER_EXPIRE，则只检查权限 */}
                {record.state === USER_EXPIRE ? (
                    <Auth code={'@base:@page:user.manage@action:edit'}>
                        <span style={{ display: 'flex' }}>
                            <Switch
                                size="small"
                                checked={text === 1}
                                onChange={() => activateUser(record)}
                            />{' '}
                        </span>
                    </Auth>
                ) : (
                    /* 其他状态保持原有逻辑 */
                    record.type != 2 && (
                        <span style={{ display: 'flex' }}>
                            <Switch
                                size="small"
                                checked={text === 1}
                                onChange={() => lockOrUnlock(text === 1, record)}
                            />{' '}
                        </span>
                    )
                )}
                    </Space>
                );
            },
        },
        {
            title: i18n.t('name', '关联角色'),
            dataIndex: 'userRoleNames',
            ellipsis: true,
            width: 250,
            render: (text: string) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '邮箱'),
            dataIndex: 'email',
            ellipsis: true,
            width: 220,
            render: (text: string) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', 'OTP验证'),
            dataIndex: 'otpState',
            ellipsis: true,
            width: 220,
            show: otpSwitch,
            render: (text: OTPStateEnum) => {
                return (
                    <Badge status={otpStateObject[text]?.state} text={otpStateObject[text]?.title} />
                );
            },
        },
        {
            title: i18n.t('name', '联系方式'),
            dataIndex: 'phoneNumber',
            ellipsis: true,
            width: 220,
            render: (text: string, record: any) => {
                return (
                    <Tooltip title={record.areaCode ? `+${record.areaCode} ${text}` : text}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {text && record.areaCode ? `+${record.areaCode} ${text}` : text || '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '到期日期'),
            show: userAutoExpireEnable,
            dataIndex: 'expireInfo',
            ellipsis: true,
            width: 220,
            render: (expireInfo: any,record: any) => {
                return <StarryAbroadOverflowEllipsisContainer>
                    {
                        getUserExpireDate(expireInfo?.expireTime,record.state === USER_EXPIRE ? undefined : expireInfo?.remainDays)
                    }
                </StarryAbroadOverflowEllipsisContainer>
            }
        },
        {
            title: i18n.t('name', '归属车组'),
            show: belongToIsOpen,
            dataIndex: 'fleets',
            ellipsis: true,
            width: 220,
            render: (text: any[]) => {
                const fleetNames = text?.map(item => item?.fleetName)?.join(",") || "-";
                return <StarryAbroadOverflowEllipsisContainer>{fleetNames}</StarryAbroadOverflowEllipsisContainer>
            }
        },
        {
            title: i18n.t('name', '指定审批人'),
            show: highRiskOperationSwitch,
            dataIndex: 'approver',
            ellipsis: true,
            width: 220,
            render: (approver) => {
                const approverNameList = (approver || []).map(item => item.approverName).join(",");
                return (
                    <Tooltip title={approverNameList}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {approverNameList || '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '创建人'),
            dataIndex: 'createUserName',
            ellipsis: true,
            width: 220,
            render: (text) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '创建时间'),
            dataIndex: 'createTime',
            ellipsis: true,
            sorter: true,
            width: 220,
            render: (text) => (
                <Tooltip title={zeroTimeStampToFormatTime(text)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {zeroTimeStampToFormatTime(text)}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作人'),
            dataIndex: 'updateUserName',
            ellipsis: true,
            width: 220,
            render: (text) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作时间'),
            dataIndex: 'updateTime',
            ellipsis: true,
            sorter: true,
            width: 220,
            render: (text) => (
                <Tooltip title={zeroTimeStampToFormatTime(text)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {zeroTimeStampToFormatTime(text) || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            ellipsis: true,
            width: 180,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <Space key="operateSpace" size={utils.constant.BASE_TABLE_OPERATE_COLUMN_SIZE}>
                        <Action
                            code="@base:@page:user.manage@action:edit"
                            url="/user-manage/edit"
                            fellback={''}
                            params={{
                                userId: record.userId,
                            }}
                        >
                            {' '}
                            <Tooltip title={i18n.t('action', '编辑')}>
                                <IcListEditFill className="opertate-icon" />
                            </Tooltip>
                        </Action>

                        {/* 对于停用、休眠、到期用户可以删除 */}
                        {(record.state === USER_UNABLE || record.state === USER_DORMANCY || record.state === USER_EXPIRE) && (
                            <Auth code="@base:@page:user.manage@action:delete">
                                <Tooltip placement="top" title={i18n.t('action', '删除')}>
                                    <IconDeleteFill
                                        className="opertate-icon"
                                        onClick={() => handleDelte(record)}
                                    />
                                </Tooltip>
                            </Auth>
                        )}
                        {/* 对于开了otp 并且绑定状态可以进行解绑操作 */}
                        {(otpSwitch && record.otpState === OTPStateEnum.BOUND) && (
                            <Tooltip placement="top" title={i18n.t('action', '解绑')}>
                                <IconUnbind03Line
                                    className="opertate-icon"
                                    onClick={() => handleUnbound(record)}
                                />
                            </Tooltip>
                        )}
                    </Space>
                );
            },
        },
    ].filter(item => isNil(item?.show) || item.show);
    // 导出数据
    const exportData = () => {
        const headersArr = [
            {
                columnName: 'account',
                title: i18n.t('name', '用户名称'),
            },
            {
                columnName: 'state',
                title: i18n.t('name', '状态'),
            },
            {
                columnName: 'userRoleNames',
                title: i18n.t('name', '关联角色'),
            },
            {
                columnName: 'email',
                title: i18n.t('name', '邮箱'),
            },
            {
                columnName: 'otpState',
                title: i18n.t('name', 'OTP验证'),
                show: otpSwitch
            },
            {
                columnName: 'phoneNumber',
                title: i18n.t('name', '联系方式'),
            },
            {
                columnName: 'expireTime',
                title: i18n.t('name', '到期日期'),
                show: userAutoExpireEnable
            },
            {
                columnName: 'belongFleet',
                title: i18n.t('name', '归属车组'),
                show: belongToIsOpen
            },
            {
                columnName: 'approver',
                title: i18n.t('name', '指定审批人'),
                show: highRiskOperationSwitch
            },
            {
                columnName: 'createUserName',
                title: i18n.t('name', '创建人'),
            },
            {
                columnName: 'createTime',
                title: i18n.t('name', '创建时间'),
            },
            {
                columnName: 'updateUserName',
                title: i18n.t('name', '操作人'),
            },
            {
                columnName: 'updateTime',
                title: i18n.t('name', '操作时间'),
            },
        ].filter(item => item?.show || isNil(item.show)).map((item, index) => ({
            ...item,
            index
        }));
        const sheetArr = [
            {
                sheetName: i18n.t('name', '用户'),
                excelHeaders: headersArr,
                queryParam: {
                    param: {
                        ...lastQueryParams,
                        pageSize: Number.MAX_SAFE_INTEGER,
                        ...(getBelongToIsOpen()?{ fields: "belongFleet"}: {})
                    },
                },
            },
        ];
        // setSpinState(true);
        exportExcel({
            serviceCode: 'b3525172036a46cfac806a1a5b329152',
            isAsync: true,
            excelType: 'XLSX',
            fileName: i18n.t('name', '用户') + `_${moment().unix()}`,
            sheetQueryParams: sheetArr,
        }).then(() => {
            message.success(i18n.t('message', '导出成功，请到个人中心中查看导出详情'));
        });
    };
    const toolbar: ProTableToolBarProps = {
        leftRender: () => (
            <Space wrap>
                <Action
                    code="@base:@page:user.manage@action:add"
                    url={'/user-manage/add'}
                    params={{ operateType: 'add' }}
                >
                    <Button type="primary" icon={<IconAddFill />}>
                        {i18n.t('action', '添加')}
                    </Button>
                </Action>
                <Button
                    icon={<IconStopFill />}
                    onClick={() => lockOrUnlock(true)}
                    disabled={selectedRowKeys.length === 0}
                >
                    {i18n.t('action', '批量停用')}
                </Button>

                <Auth code="@base:@page:user.manage@action:delete">
                    <Button
                        icon={<IconDeleteFill />}
                        onClick={handleDelte}
                        disabled={selectedRowKeys.length === 0}
                    >
                        {i18n.t('action', '批量删除')}
                    </Button>
                </Auth>
            </Space>
        ),
        columnSetting: {
            storageKey: '@base:@page:user.manage',
            disabledKeys: ['account', 'operate'],
        },
        iconBtns: [
            // <Link
            //     to={`/user-manage/import`}
            // >
            //     <IconImport title={i18n.t('action', '导入')}></IconImport>
            // </Link>,
            <Tooltip title={i18n.t('action', '导出')} placement="top" key="action-top">
                <span>
                    <StarryAbroadIcon>
                        <IconExport onClick={exportData} />
                    </StarryAbroadIcon>
                </span>
            </Tooltip>,
            'reload',
            'column-setting',
        ],
    };
    const rowSelection = {
        selectedRowKeys,
        getCheckboxProps: (record: any) => ({
            disabled: record.type == 2,
        }),
        onChange: (selectedKeys: any) => {
            setSelectedRowKeys(selectedKeys);
        },
    };
    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="user-manage-container">
                    <StarryTable
                        columns={columns}
                        queryProps={{
                            items: formItems,
                            form,
                        }}
                        scroll={{x:'100%'}}
                        aroundBordered
                        pagination={{
                            defaultCurrent: Number(page) || 1,
                            defaultPageSize: Number(pageSize) || 20,
                        }}
                        fetchDataAfterMount={false}
                        fetchDataFunc={fetchUserList}
                        rowSelection={rowSelection}
                        rowKey="userId"
                        toolbar={toolbar}
                        ref={tableRef}
                    />
                    {visible && <ActivateUserModal
                        visible={visible}
                        onOk={() => {
                            setSelectedRecord(undefined);
                            setVisible(false);
                            // @ts-ignore
                            tableRef.current.reload();
                        }}
                        onCancel={() => {
                            setSelectedRecord(undefined);
                            setVisible(false);
                        }}
                        userId={selectedRecord?.userId}
                    />}
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
