import { useEffect, useRef, useState } from 'react';
import { useAsyncEffect, useUpdateEffect } from '@streamax/hooks';
import { mosaicManager, MosaicTypeEnum, useWebsocketMessage } from '@base-app/runtime-lib';
import { calcEvidenceInfo } from '../../../utils/evidence';
import { fetchParameterDetail } from '../../../service/parameter';
import { useAlarmState } from '../../..';
import {
    AlarmDataItemWithEvidence,
    EVIDENCE_FILE_MOSAIC_TYPE,
    extendFns,
    MAX_LENGTH,
    MG,
    syncLatestEvidenceInfo,
} from '..';
import { getMessageNoticeConfig, updateMessageNoticeConfig } from '../../../service/message';
import { usePerformanceStrategy } from '@/modules';


export interface WSMessage {
    message: AlarmDataItemWithEvidence;
    messageType: number;
}

export interface NewPushedAlarm {
    isAdd: boolean; // 有策略新增
    alarm: AlarmDataItemWithEvidence;
}

// 单位秒
const PUSH_DELAY_TIME = 1;
const AlarmTopic = 'b.real_time_alarm_v2';
const topics = [AlarmTopic, 'b.real_time_alarm_handle', 'b.real_time_alarm_evidence'];

// #129363 推送消息没有进行马赛克权限过滤，前端兜底过滤
function filterFileListWithMosaic(fileList: { mosaicType: EVIDENCE_FILE_MOSAIC_TYPE }[]) {
    if(!Array.isArray(fileList)) return [];
    const hasMosaic = mosaicManager.checkMosaicEnable(MosaicTypeEnum.alarm);
    const mosaic = hasMosaic ? EVIDENCE_FILE_MOSAIC_TYPE.WITH_MOSAIC : EVIDENCE_FILE_MOSAIC_TYPE.WITHOUT_MOSAIC;
    const codeList = [EVIDENCE_FILE_MOSAIC_TYPE.UNKNOWN, mosaic];

    return fileList.filter(file=>{
        return codeList.includes(file.mosaicType);
    });
}

export default () => {
    const [realtimeAlarm, setRealtimeAlarm] = useState<Record<string, any>[]>([]);
    const setRealtimeAlarmRef = useRef(setRealtimeAlarm);
    setRealtimeAlarmRef.current = setRealtimeAlarm;
    const { send, readyState, subscribe, unsubscribe, addEvent } = useWebsocketMessage();
    const socketStartedRef = useRef(false);
    // 连接时处理
    const onSocketConnect = () => {
        const { getState, setState } = useAlarmState;
        syncLatestEvidenceInfo(getState, setState);
    };
    const subscribeAlarm = () => {
        console.log('alarmModule subscribeAlarm');
        const pushDelayTime =  1000 * (usePerformanceStrategy.getState().currentStrategy.realtimeAlarmUIUpdateInterval || PUSH_DELAY_TIME);
        console.log('pushDelayTime', pushDelayTime);
        const subscribes = topics.map(topic => {
            if (!socketStartedRef.current) {
                // todo 确认这里几个聚合，是否同频
                return subscribe(
                    topic,
                    (result) => {
                        setRealtimeAlarmRef.current(result);
                        // 这里接受到的data值就为所订阅的主题推送的内容
                    },
                    {
                        retryTimes: 3,
                        pushDelayTime,
                        customSend: {
                            type: 'b.msg_type',
                            values: [topic],
                            // increment: false,
                            limitValue: AlarmTopic === topic ? useAlarmState.getState().alarmFrequency : null
                        }
                    },
                );
            } else {
                send(
                    [
                        'b.subscribe',
                        {
                            type: 'b.msg_type',
                            values: [topic],
                            limitValue: AlarmTopic === topic ? useAlarmState.getState().alarmFrequency : null
                        },
                    ],
                    () => {},
                    { topic, pushDelayTime },
                );
            }
        });

        Promise.all(subscribes).catch((err) => {
            if (!socketStartedRef.current) {
                // 重试
                setTimeout(() => {
                    subscribeAlarm();
                }, 500);
            }
            socketStartedRef.current = false;
            console.error(err);
            // 在达到重试次数后依然失败，可以在这里处理一些业务逻辑
        });
        socketStartedRef.current = true;
    };

    const unsubscribeAlarm = () => {
        topics.forEach(topic => {
            unsubscribe(topic, () => {});
        });
    };

    const initFilterRef = useRef(false);
    const filterAlarm =(alarmTypes: number[], disableAlarmTypes: number[] = []) => {
        if (!initFilterRef.current) {
            getMessageNoticeConfig({
                configKey: 'userSubjectAlarmType',
            }).then((res) => {
                let { enableAlarmType = [] } = res.userSubjectAlarmType;
                const config = {
                    editStatus: 0,
                    enableAlarmType: enableAlarmType,
                    disableAlarmType: disableAlarmTypes,
                };
                // 后端无法知道初始报警类型，若未编辑过 需调用更新接口
                if (!res.userSubjectAlarmType.editStatus) {
                    enableAlarmType = alarmTypes;
                    config.enableAlarmType = enableAlarmType;
                    updateMessageNoticeConfig({
                        configKey: 'userSubjectAlarmType',
                        configValue: JSON.stringify(config),
                    }).then(() => {
                        // todo 过滤状态是否纳入到store管理？
                        // 保存到store
                        // ...
                    });
                }
                topics.forEach(topic => {
                    // 订阅自定义实时报警标签类型
                    subscribe(topic + '_custom', () => {}, {
                        retryTimes: 3,
                        customSend: {
                            type: topic + "_alarm_type",
                            values: enableAlarmType,
                            increment: false,
                        },
                    }).catch((err: any) => {
                        console.error(err);
                    });
                });
                initFilterRef.current = true;
                config.editStatus = res.userSubjectAlarmType?.editStatus || 0;
                useAlarmState.setState({
                    alarmConfig: config,
                });
            });
        } else {
            const config = {
                editStatus: 1,
                enableAlarmType: alarmTypes,
                disableAlarmType: [],
            };
            // 保存用户报警类型过滤条件
            updateMessageNoticeConfig({
                configKey: 'userSubjectAlarmType',
                configValue: JSON.stringify(config),
            }).then(() => {
                // todo 过滤状态是否纳入到store管理？
                // 保存到store
                // ...
            });
            topics.forEach(topic => {
                send(
                    [
                        'b.subscribe',
                        {
                            type: topic + "_alarm_type",
                            values: alarmTypes,
                            increment: false,

                        },
                    ],
                    () => {},
                    { topic: topic + '_custom' },
                );
            });
            useAlarmState.setState({
                alarmConfig: config,
            });
        }
    };

    // socket 断开重连
    useUpdateEffect(() => {
        if (readyState === 1 && socketStartedRef.current) {
            // 由于切换服务入口会重新连soket 但实时报警要一直订阅
            // 订阅新增报警
            subscribeAlarm();
        }
    }, [readyState]);
    useEffect(() => {

        addEvent('onReconnection', (data: any) => {
            if (data?.state === 'finish') onSocketConnect();
        });

        fetchParameterDetail(
            {
                parameterKey: 'VIDEO.DRAW.FRAME.SHOW',
            },
            false,
        ).then((data: any) => {
            useAlarmState.setState({
                videoDrawFrameShow: data.parameterValue || '0'
            });
        });
        setTimeout(() => {
            const unSubscribeAlarmFrequencyRef = { current: () => {} };
            const socket = {
                init: true,
                hasStart: false,
                start: () => {
                    if (!socket.hasStart) {
                        const syncAlarmFrequency = (newAlarmFrequency: number) => {
                            if (newAlarmFrequency !== useAlarmState.getState().alarmFrequency) {
                                useAlarmState.setState({
                                    alarmFrequency: newAlarmFrequency
                                });
                            };
                        };
                        syncAlarmFrequency(usePerformanceStrategy.getState().currentStrategy.realtimeAlarmPushFrequency);
                        unSubscribeAlarmFrequencyRef.current = usePerformanceStrategy.subscribe((newState, prevState) => {
                            syncAlarmFrequency(newState.currentStrategy.realtimeAlarmPushFrequency);
                            if (newState.currentStrategy.realtimeAlarmUIUpdateInterval !== prevState.currentStrategy.realtimeAlarmUIUpdateInterval) {
                                subscribeAlarm();
                            };
                        });
                        socket.hasStart = true;
                    }
                    subscribeAlarm(); // 初始化订阅
                },
                stop: () => {
                    // 更新状态，停止订阅推送频率
                    socket.hasStart = false;
                    unSubscribeAlarmFrequencyRef.current();
                    unsubscribeAlarm();
                },
                filter: (alarmTypes: number[], disableAlarmTypes: number[] = []) => {
                    filterAlarm(alarmTypes, disableAlarmTypes);
                }
            };
            useAlarmState.setState({
                socket
            });
        }, 0);

    }, []);

    useAsyncEffect(async () => {
        if (realtimeAlarm.length) {
            // todo 这里需要确保alarmList alarmId唯一
            let { realTimeAlarmList: alarmList, videoDrawFrameShow } = useAlarmState.getState();
            // let alarmList = state.realTimeAlarmList;
            let newPushedAlarm: NewPushedAlarm = null as unknown as  NewPushedAlarm;
            useAlarmState.setState({
                newPushedAlarm: {
                    alarm: realtimeAlarm[realtimeAlarm.length - 1]?.message,
                    isAdd: false
                },
            });
            const currentStrategy = usePerformanceStrategy.getState().currentStrategy;
            // 批量推送
            for (let i = 0; i < realtimeAlarm.length; i++) {
                const realtimeAlarmItem = realtimeAlarm[i];
                if (Object.keys(realtimeAlarmItem).length) {
                    // @ts-ignore
                    const { messageType, message } = realtimeAlarmItem;
                    const { alarmId, evidenceStatus } = message || {};
                    let evidenceInfo = {};

                    if (messageType == 1) {
                        /**
                         * 报警地址初始就会有
                         * 若地址未解析出 还要解析出地址
                         */
                        // const cacheAlarmItem = alarmList.find((item) => item.alarmId === alarmId);
                        // if (cacheAlarmItem) {
                        //     message.address = cacheAlarmItem.address;
                        // } else {
                        //     /**地址解析放到basePages，防止在每个页面都在请求地址 */
                        //     // if(!message.address && lat && lng){
                        //     //     message.address = (await getAddress({lat,lng})) || i18n.t('name', '无位置信息');
                        //     // }else if(!message.address && !lat && !lng){
                        //     //     message.address = i18n.t('name', '无位置信息');
                        //     // }
                        // }
                        // source用于标记数据来源于中台，而非行业拓展的报警数据
                        const messageCache = { ...message, source: MG , realtimeAlarmShowEvidenceInfo: currentStrategy.realtimeAlarmShowEvidenceInfo };
                        alarmList.push(messageCache);
                        // 将新增的第一条，设置为当前需要关注的一条
                        newPushedAlarm = newPushedAlarm || {
                            isAdd: true,
                            alarm: messageCache
                        };
                    }
                    if (messageType == 2) {
                        // 证据状态变更，更新到对应报警消息上
                        if (evidenceStatus == 3) {
                            const {
                                alarmId,
                                alarmType,
                                evidenceStatus,
                                authId,
                                protocolType,
                                fileList
                            } = message || {};
                            // 证据下载完成
                            evidenceInfo = {
                                vehicleInfo: {
                                    alarmId,
                                    alarmType,
                                    evidenceStatus,
                                    authId,
                                    protocolType,
                                },
                                fileList: filterFileListWithMosaic(fileList),
                                videoChannelList: message?.videoChannelList || []
                            };
                        }
                        alarmList = alarmList.map((item: any) => {
                            if (item.alarmId == alarmId) {
                                let newAlarmData;
                                if (evidenceStatus === 3) {
                                    newAlarmData = {
                                        ...item,
                                        ...message,
                                        evidenceInfo,
                                        ...calcEvidenceInfo(evidenceInfo, videoDrawFrameShow),
                                        // todo 这里是否能直接使用evidenceInfo.vehicleInfo?
                                        vehicleInfo: {
                                            //@ts-ignore
                                            authId: message?.authId,
                                        },
                                    };
                                } else {
                                    newAlarmData = {
                                        ...item,
                                        evidenceInfo,
                                        ...message,
                                        // todo 这里是否能直接使用evidenceInfo.vehicleInfo?
                                        vehicleInfo: {
                                            //@ts-ignore
                                            authId: message?.authId,
                                        },
                                    };
                                }
                                return newAlarmData
                            };
                            return item;
                        });
                    };
                    if (messageType == 3 && message.alarmStatus == 1) {
                        alarmList = alarmList.filter((item: any) => item.alarmId !== alarmId);
                    }
                }
            }
            alarmList.sort((a, b) => b.alarmTime - a.alarmTime);
            // 限制报警列表最大长度
            if (alarmList.length >= MAX_LENGTH) {
                // 最大缓存100条
                alarmList.length = MAX_LENGTH;
            }
            if (extendFns.length) {
                alarmList = extendFns.reduce((list, extendFn) => {
                    return extendFn(list);
                }, alarmList);
                // 这里更新一次newPushedAlarm，是为了确保该报警被用户拓展（前面extendFns流程）属性能同步过来
                if (newPushedAlarm) {
                    newPushedAlarm.alarm = alarmList.find(item => item.alarmId === newPushedAlarm.alarm.alarmId) || newPushedAlarm.alarm;
                }
            }

            useAlarmState.setState({
                realTimeAlarmList: [...alarmList],
                newPushedAlarm
            });
        }
    }, [realtimeAlarm]);
};
